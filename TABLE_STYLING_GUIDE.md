# BOG Angular Application - Unified Table Styling Guide

## Overview
This guide explains how to apply consistent table styling across all components in the BOG Angular application. The styling has been designed to match the provided design image with the primary color theme (#2D336B) and consistent visual elements.

## Key Design Elements
Based on the provided design image, the table styling includes:

1. **Header**: Dark blue background (#2D336B) with white text
2. **Borders**: Clean 1px borders between all cells (#E2E8F0)
3. **Text**: Center-aligned data in all cells
4. **Font**: 12px font size with proper font weights
5. **Hover Effects**: Subtle background color change on row hover
6. **Action Buttons**: Consistent edit/delete button styling
7. **QR Codes**: Properly sized and aligned QR code images

## Implementation

### 1. Global Styles Applied
The following files have been updated with unified table styling:

- `src/assets/scss/unified-table-styles.scss` - Main table styling file
- `src/assets/scss/new-theme.scss` - Updated table section
- `src/assets/scss/table.scss` - Updated for consistency
- `src/styles.scss` - Imports the unified styles

### 2. Component-Level Changes
Each component's SCSS file should be updated to use the unified styles:

```scss
// Import unified table styles - all styling is now handled globally
@import '../../../../assets/scss/unified-table-styles.scss';

// Component-specific overrides (if needed)
.table-container {
    // All styling is now handled by unified-table-styles.scss
    // This ensures consistency across all components
}
```

### 3. HTML Structure
Ensure your table HTML follows this structure:

```html
<div class="table-container">
    <div class="table-responsive">
        <table class="table table-bordered table-hover">
            <thead class="table-header">
                <tr>
                    <th>Column 1</th>
                    <th>Column 2</th>
                    <th class="actions">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Data 1</td>
                    <td>Data 2</td>
                    <td class="actions">
                        <button class="btn btn-sm btn-warning edit-btn">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-danger delete-btn">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
```

### 4. Special Column Types

#### QR Code Column
```html
<td class="qr-code-cell">
    <img [src]="qrCodeUrl" alt="QR Code">
</td>
```

#### Status Column
```html
<td class="status-cell">
    <span class="badge bg-success">Active</span>
</td>
```

#### Plant Chips
```html
<td>
    <span class="plant-chip">Plant Name</span>
</td>
```

#### Role Badges
```html
<td>
    <span class="role-badge">Admin</span>
</td>
```

#### Details Cell (like plant-management)
```html
<td class="details-cell">
    <div class="details-container">
        <div class="label-value">
            <strong>Plant Name:</strong>
            <span class="value-text">{{plant.name}}</span>
        </div>
        <div class="label-value">
            <strong>Location:</strong>
            <span class="value-text">{{plant.location}}</span>
        </div>
    </div>
</td>
```

## Components Updated
The following components have been updated with unified table styling:
- `src/app/components/master-management/plant-type/`
- `src/app/components/master-management/designation/`
- `src/app/components/master-management/equipment/`
- `src/app/components/master-management/opco/`
- `src/app/components/master-management/root-cause/`
- `src/app/components/master-management/segment/`
- `src/app/components/master-management/inspection-tool/`
- `src/app/components/master-management/area/`

## Components That Still Need Updates
Apply the same pattern to these remaining components:

### Master Management (Remaining)
- `src/app/components/master-management/cluster/`
- `src/app/components/master-management/location-type/`
- `src/app/components/master-management/qr-type/`
- `src/app/components/master-management/incident-master/`

### Other Component Categories
- All components in `src/app/components/admin-management/`
- All components in `src/app/components/report-management/`
- All components in `src/app/components/digisafe/`
- All components in `src/app/components/notification-management/`
- All components in `src/app/components/qrcode-management/`
- `src/app/components/crisis-management/`
- `src/app/components/leaderboard/`

### Quick Update Pattern
For each component, replace the existing table SCSS with:
```scss
// Import unified table styles - all styling is now handled globally
@import '../../../../assets/scss/unified-table-styles.scss';

// Component-specific overrides (if needed)
.table-container {
    // All styling is now handled by unified-table-styles.scss
    // This ensures consistency across all components
}
```

## CSS Classes Available

### Table Classes
- `.unified-table` - Main table class
- `.table` - Bootstrap table class (enhanced)
- `.custom-table` - Alternative table class
- `.table-header` - Header styling
- `.table-container` - Container with overflow handling
- `.table-responsive` - Responsive wrapper

### Column Classes
- `.actions` - Action column styling
- `.qr-code-cell` - QR code column
- `.status-cell` - Status column
- `.details-cell` - Detailed information cell

### Component Classes
- `.plant-chip` - Plant name chips
- `.role-badge` - Role badges
- `.edit-btn` - Edit button styling
- `.delete-btn` - Delete button styling

## Color Scheme
- **Primary Blue**: #2D336B (table headers)
- **Secondary Blue**: #41559B
- **Border Color**: #E2E8F0
- **Text Color**: #334155
- **Hover Background**: #F8FAFC

## Responsive Design
The table styling includes responsive breakpoints:
- Mobile devices: Reduced padding and font sizes
- Tablet and desktop: Full styling applied

## Testing
After applying the changes:
1. Check that all tables have consistent header colors
2. Verify borders appear correctly between cells
3. Test hover effects on table rows
4. Ensure action buttons are properly styled
5. Confirm QR codes display at correct size
6. Test responsive behavior on different screen sizes

## Migration Steps
1. Update component SCSS files to import unified styles
2. Remove duplicate table styling from individual components
3. Update HTML templates to use correct CSS classes
4. Test each component to ensure styling is applied correctly
5. Make component-specific adjustments if needed
