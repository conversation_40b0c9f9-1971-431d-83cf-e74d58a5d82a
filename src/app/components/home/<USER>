<app-header></app-header>
<div class="d-flex vh-100 overflow-auto sidebar-container">
    <nav id="sidebar" [class.collapsed]="isCollapsed">
        <div class="menu-header" *ngIf="!isCollapsed">Menu</div>
        <div class="list-group list-group-flush">
            <div *ngFor="let nav of filteredNavItems; let firstItem = first">
                <a class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                    href="javascript:void(0)"
                    [class.active]="isNavItemActive(nav, activeNav)"
                    [class.parent-of-active]="hasActiveChild(nav, activeNav) && !isDirectlyActive(nav, activeNav)"
                    (click)="nav.subitems.length ? toggleSubMenu(nav) : activateNav(nav.route)">
                    <div *ngIf="isNavItemActive(nav, activeNav)" class="active-indicator"></div>
                    <span class="d-flex align-items-center nav-item-content">
                        <i [class]="nav.icon" class="iconStyle"></i>
                        <span class="nav-title">{{ nav.title }}</span>
                    </span>
                    <i *ngIf="nav.subitems?.length"
                        [class]="nav.expanded ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" class="submenu-icon"></i>
                </a>

                <div *ngIf="nav.subitems?.length && nav.expanded" class="ms-3 submenu">
                    <div *ngFor="let child of nav.subitems; let firstSubItem = first">
                        <a class="list-group-item list-group-item-action d-flex justify-content-between align-items-center"
                            href="javascript:void(0)"
                            [class.active]="isNavItemActive(child, activeNav)"
                            [class.parent-of-active]="hasActiveChild(child, activeNav) && !isDirectlyActive(child, activeNav)"
                            (click)="child.subitems.length ? toggleSubMenu(child) : activateNav(child.route)">
                            <div *ngIf="isNavItemActive(child, activeNav)" class="active-indicator"></div>
                            <span class="d-flex align-items-center nav-item-content">
                                <i [class]="child.icon" class="iconStyle"></i>
                                <span class="nav-title">{{ child.title }}</span>
                            </span>
                            <i *ngIf="child.subitems?.length"
                                [class]="child.expanded ? 'bi bi-chevron-down' : 'bi bi-chevron-right'" class="submenu-icon"></i>
                        </a>

                        <div *ngIf="child.subitems.length && child.expanded" class="ms-4 submenu">
                            <div *ngFor="let subchild of child.subitems; let firstSubSubItem = first">
                                <a class="list-group-item list-group-item-action" href="javascript:void(0)"
                                    [class.active]="isNavItemActive(subchild, activeNav)" (click)="activateNav(subchild.route)">
                                    <div *ngIf="isNavItemActive(subchild, activeNav)" class="active-indicator"></div>
                                    <span class="d-flex align-items-center nav-item-content">
                                        <i [class]="subchild.icon" class="iconStyle"></i>
                                        <span class="nav-title">{{ subchild.title }}</span>
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex-grow-1 p-3 overflow-auto content-wrapper">
        <router-outlet></router-outlet>
    </div>
</div>