<app-toast-message></app-toast-message>

<!-- Tab Header with Filter and Download buttons -->
<div class="tab-header-container">
    <div class="tab-spacer"></div>

    <div class="tab-center">
        <h6 class="mb-0">Location Types</h6>
    </div>

    <div class="tab-actions">
        <!-- Download Excel Button with Dropdown -->
        <div ngbDropdown class="d-inline-block">
            <button type="button" class="btn adani-btn dropdown-toggle" id="downloadLocTypeExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadLocTypeExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (locationTypeList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ locationTypeList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>

        <!-- Create Button -->
        <button class="btn adani-btn" (click)="openCreateModal()" title="Create New Location Type">
            <i class="bi bi-plus-circle me-1"></i> Create New
        </button>

        <!-- Filter Button -->
        <img src="../../../assets/svg/filter.svg" class="filter-button" (click)="openFilterModal()" alt="Filter"
            style="width: 35px; height: 35px; cursor: pointer;" />
    </div>
</div>

<!-- Table without card wrapper -->
<div class="table-responsive">
    <table class="table table-bordered table-hover">
        <thead class="table-header">
            <tr class="text-center">
                <th scope="col">Enabled/Disabled</th>
                <th scope="col">Location Type Name</th>
                <th scope="col">Actions</th>
            </tr>
        </thead>
        <tbody>
            <!-- Loading Indicator -->
            <tr *ngIf="listLoading">
                <td colspan="3" class="text-center p-4">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Loading location types...
                </td>
            </tr>
            <!-- No Data Message -->
            <tr *ngIf="!listLoading && locationTypeList.length === 0">
                <td colspan="3" class="text-center p-4 text-muted">
                    No location types found matching the current filters.
                </td>
            </tr>
            <!-- Data Rows -->
            <tr *ngFor="let locationType of locationTypeList">
                <td class="text-center">
                    <app-switch
                        [(checked)]="locationType.enabled"
                        [requireConfirmation]="true"
                        (checkedChange)="onSwitchToggle($event, locationType)"
                        onLabel="Active" offLabel="Inactive">
                    </app-switch>
                </td>
                <td class="text-center">{{ locationType.title }}</td>
                <td class="actions text-center">
                    <button class="btn btn-sm adani-btn" (click)="openEditModal(locationType)" title="Edit Location Type">
                        <i class="bi bi-pencil edit"></i> Edit
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Pagination outside table -->
<app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"></app-pagination>

<!-- Filter Offcanvas (Renamed *ngIf variable) -->
<app-offcanvas [title]="'Filter Location Types'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
<div class="filter-container p-3">
    <form #filterForm="ngForm" (ngSubmit)="filterForm.valid && applyFilters()">
        <div class="row g-3">

            <div class="col-12">
                <label class="form-label" for="filterLocTypeName">Location Type Name</label>
                <input type="text" id="filterLocTypeName" class="form-control" placeholder="Search by Location Type Name"
                       [(ngModel)]="filters.name" name="name" maxlength="30" pattern="^[a-zA-Z ]*$" #filterNameInput="ngModel">
                <!-- Character count display - only visible when there's text -->
                <small *ngIf="filters.name" class="text-muted d-block text-end mt-1">
                    {{ filters.name.length }}/30 characters
                </small>
                <div *ngIf="filterNameInput.invalid && (filterNameInput.dirty || filterNameInput.touched)" class="text-danger small mt-1">
                    <div *ngIf="filterNameInput.errors?.['pattern']">Location type name should contain only alphabets and spaces.</div>
                    <div *ngIf="filterNameInput.errors?.['maxlength']">Maximum 30 characters allowed.</div>
                </div>
            </div>

             <div class="col-12">
                <label class="form-label" for="filterEnabledLocType">Enabled Status</label>
                <select id="filterEnabledLocType" class="form-select"
                        [(ngModel)]="filters.enabled" name="enabled">
                    <option [ngValue]="null">Any</option>
                    <option value="true">Yes</option>
                    <option value="false">No</option>
                </select>
            </div>

            <div class="col-12">
                <label class="form-label" for="filterSortByLocType">Sort By</label>
                <select id="filterSortByLocType" class="form-select"
                        [(ngModel)]="filters.sortField" name="sortField">
                    <option [ngValue]="null">Default Sort (Name ASC)</option>
                    <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                </select>
                 <label class="form-label mt-2" for="filterSortDirLocType">Sort Direction</label>
                 <select id="filterSortDirLocType" class="form-select"
                        [(ngModel)]="filters.sortDirection" name="sortDirection">
                    <option value="ASC">Ascending</option>
                    <option value="DESC">Descending</option>
                </select>
            </div>

            <div class="col-12 mt-4 d-grid gap-2">
                <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                    <i class="bi bi-search me-1"></i> Search
                </button>
                 <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                    <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>

<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit Location Type'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
<div class="edit-container p-3">
    <form *ngIf="selectedLocationType" #editForm="ngForm" (ngSubmit)="submitEditForm()">
        <div class="row g-3">

            <!-- Location Type ID (Readonly) -->
            <div class="col-12">
                <label class="form-label" for="editLocTypeId">Location Type ID</label>
                <input type="text" id="editLocTypeId" class="form-control"
                       [value]="selectedLocationType.id" name="id" readonly disabled>
            </div>

            <!-- Location Type Name (Editable) -->
            <div class="col-12">
                <label class="form-label" for="editLocTypeName">Location Type Name</label>
                <input type="text" id="editLocTypeName" class="form-control" placeholder="Enter Location Type Name"
                       [(ngModel)]="selectedLocationType.title" name="title" required maxlength="30"
                       pattern="^[a-zA-Z ]*$" #titleInput="ngModel">
                <!-- Character count display - only visible when there's text -->
                <small *ngIf="selectedLocationType.title" class="text-muted d-block text-end mt-1">
                    {{ selectedLocationType.title.length }}/30 characters
                </small>
                <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="text-danger small mt-1">
                    <div *ngIf="titleInput.errors?.['required']">Location type name is required.</div>
                    <div *ngIf="titleInput.errors?.['pattern']">Location type name should contain only alphabets and spaces.</div>
                    <div *ngIf="titleInput.errors?.['maxlength']">Maximum 30 characters allowed.</div>
                </div>
            </div>

             <!-- Enabled Status -->
             <div class="col-12">
                 <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="selectedLocationType.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
             </div>

            <!-- Action Buttons -->
            <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                    Cancel
                </button>
                <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                     <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                     <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                     <span *ngIf="editLoading"> Saving...</span>
                </button>
            </div>
        </div>
    </form>
    <div *ngIf="!selectedLocationType && isEditModalOpen" class="text-center p-5">
         <div class="spinner-border spinner-border-sm" role="status">
             <span class="visually-hidden">Loading form...</span>
         </div>
    </div>
</div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

<!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New Location Type'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
<div class="create-container p-3">
    <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
        <div class="row g-3">

            <!-- Location Type Name (Required) -->
            <div class="col-12">
                <label class="form-label" for="createLocTypeName">Location Type Name</label>
                <input type="text" id="createLocTypeName" class="form-control" placeholder="Enter New Location Type Name"
                       [(ngModel)]="newLocationTypeData.title" name="title" required maxlength="30"
                       pattern="^[a-zA-Z ]*$" #createTitleInput="ngModel">
                <!-- Character count display - only visible when there's text -->
                <small *ngIf="newLocationTypeData.title" class="text-muted d-block text-end mt-1">
                    {{ newLocationTypeData.title.length }}/30 characters
                </small>
                <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="text-danger small mt-1">
                    <div *ngIf="createTitleInput.errors?.['required']">Location type name is required.</div>
                    <div *ngIf="createTitleInput.errors?.['pattern']">Location type name should contain only alphabets and spaces.</div>
                    <div *ngIf="createTitleInput.errors?.['maxlength']">Maximum 30 characters allowed.</div>
                </div>
            </div>

            <!-- Enabled Status (Default to Active/true) -->
            <div class="col-12">
                <label class="form-label d-block mb-2">Status</label>
                <app-switch
                      [(checked)]="newLocationTypeData.enabled"
                      name="enabled"
                      onLabel="Active"
                      offLabel="Inactive">
                </app-switch>
            </div>

            <!-- Action Buttons -->
            <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                    Cancel
                </button>
                <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                    <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create Location Type</span>
                    <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span *ngIf="createLoading"> Creating...</span>
                </button>
            </div>
        </div>
    </form>
</div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
