// Import unified table styles - all styling is now handled globally
@import '../../../../assets/scss/unified-table-styles.scss';

// Component-specific overrides (if needed)
.table-container {
    // All styling is now handled by unified-table-styles.scss
    // This ensures consistency across all components
}

// Any component-specific customizations can be added here
// For example, if this component needs special column widths:
// .plant-type-table {
//     .col-name { width: 40%; }
//     .col-description { width: 40%; }
//     .col-actions { width: 20%; }
// }