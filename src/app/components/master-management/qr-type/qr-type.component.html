<app-toast-message></app-toast-message>

<!-- Tab Header with Filter and Download buttons -->
<div class="tab-header-container">
    <div class="tab-spacer"></div>

    <div class="tab-center">
        <h6 class="mb-0">QR Types</h6>
    </div>

    <div class="tab-actions">
        <!-- Download Excel Button with Dropdown -->
        <div ngbDropdown class="d-inline-block">
            <button type="button" class="btn adani-btn dropdown-toggle" id="downloadQrTypeExcelDropdown" ngbDropdownToggle
                [disabled]="isDownloadingExcel || listLoading">
                <span *ngIf="!isDownloadingExcel">
                    <i class="bi bi-file-earmark-excel me-1"></i> Download Excel
                </span>
                <span *ngIf="isDownloadingExcel">
                    <span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                    Downloading {{ downloadType === 'current' ? 'Page' : (downloadType === 'all' ? 'All' : '') }}...
                </span>
            </button>
            <ul ngbDropdownMenu aria-labelledby="downloadQrTypeExcelDropdown">
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('current')" [disabled]="isDownloadingExcel || listLoading || (qrTypeList?.length ?? 0) === 0">
                        <i class="bi bi-download me-1"></i> Download Current Page ({{ qrTypeList?.length ?? 0 }})
                    </button>
                </li>
                <li>
                    <button ngbDropdownItem (click)="downloadExcel('all')" [disabled]="isDownloadingExcel || listLoading || totalItems === 0">
                        <i class="bi bi-cloud-download me-1"></i> Download All Filtered ({{ totalItems }})
                    </button>
                </li>
            </ul>
        </div>

        <!-- Create Button -->
        <button class="btn adani-btn" (click)="openCreateModal()" title="Create New QR Type">
            <i class="bi bi-plus-circle me-1"></i> Create New
        </button>

        <!-- Filter Button -->
        <img src="../../../assets/svg/filter.svg" class="filter-button" (click)="openFilterModal()" alt="Filter"
            style="width: 35px; height: 35px; cursor: pointer;" />
    </div>
</div>

<!-- Table without card wrapper -->
<div class="table-responsive">
    <table class="table table-bordered table-hover">
        <thead class="table-header">
            <tr class="text-center">
                <th scope="col">Enabled/Disabled</th>
                <th scope="col">QR Type Name</th>
                <th scope="col">Actions</th>
            </tr>
        </thead>
        <tbody>
            <!-- Loading Indicator -->
            <tr *ngIf="listLoading">
                <td colspan="3" class="text-center p-4">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Loading QR types...
                </td>
            </tr>
            <!-- No Data Message -->
            <tr *ngIf="!listLoading && qrTypeList.length === 0">
                <td colspan="3" class="text-center p-4 text-muted">
                    No QR types found matching the current filters.
                </td>
            </tr>
            <!-- Data Rows -->
            <tr *ngFor="let qr of qrTypeList">
                <td class="text-center">
                    <app-switch
                        [(checked)]="qr.enabled"
                        [requireConfirmation]="true"
                        (checkedChange)="onSwitchToggle($event, qr)"
                        onLabel="Active" offLabel="Inactive">
                    </app-switch>
                </td>
                <td class="text-center">{{ qr.title }}</td>
                <td class="actions text-center">
                    <button class="btn btn-sm adani-btn" (click)="openEditModal(qr)" title="Edit QR Type">
                        <i class="bi bi-pencil edit"></i> Edit
                    </button>
                </td>
            </tr>
        </tbody>
    </table>
</div>

<!-- Pagination outside table -->
<app-pagination [currentPage]="currentPage" [totalItems]="totalItems" [itemsPerPage]="itemsPerPage"
    (pageChange)="onPageChange($event)"></app-pagination>

<!-- Filter Offcanvas (Renamed *ngIf variable) -->
<app-offcanvas [title]="'Filter QR Types'" *ngIf="isFilterModalOpen" (onClickCross)="closeFilterModal()">
  <div class="filter-container p-3">
      <form #filterForm="ngForm" (ngSubmit)="applyFilters()">
          <div class="row g-3">

              <div class="col-12">
                  <label class="form-label" for="filterQrTypeName">QR Type Name</label>
                  <input type="text" id="filterQrTypeName" class="form-control" placeholder="Search by QR Type Name"
                         [(ngModel)]="filters.name" name="name" #filterName="ngModel"
                         pattern="^[a-zA-Z\s]*$" maxlength="30"
                         [ngClass]="{'is-invalid': filterName.invalid && (filterName.dirty || filterName.touched)}">
                  <div *ngIf="filterName.invalid && (filterName.dirty || filterName.touched)" class="invalid-feedback">
                      <div *ngIf="filterName.errors?.['pattern']">QR type name should contain only alphabets.</div>
                  </div>
              </div>

               <div class="col-12">
                  <label class="form-label" for="filterEnabledQr">Enabled Status</label>
                  <select id="filterEnabledQr" class="form-select"
                          [(ngModel)]="filters.enabled" name="enabled">
                      <option [ngValue]="null">Any</option>
                      <option value="true">Yes</option>
                      <option value="false">No</option>
                  </select>
              </div>

              <div class="col-12">
                  <label class="form-label" for="filterSortByQr">Sort By</label>
                  <select id="filterSortByQr" class="form-select"
                          [(ngModel)]="filters.sortField" name="sortField">
                      <option [ngValue]="null">Default Sort (Name ASC)</option>
                      <option *ngFor="let field of availableSortFields" [value]="field.value">{{ field.label }}</option>
                  </select>
                   <label class="form-label mt-2" for="filterSortDirQr">Sort Direction</label>
                   <select id="filterSortDirQr" class="form-select"
                          [(ngModel)]="filters.sortDirection" name="sortDirection">
                      <option value="ASC">Ascending</option>
                      <option value="DESC">Descending</option>
                  </select>
              </div>

              <div class="col-12 mt-4 d-grid gap-2">
                  <button type="submit" class="btn adani-btn" [disabled]="filterForm.invalid">
                      <i class="bi bi-search me-1"></i> Search
                  </button>
                   <button type="button" class="btn btn-secondary" (click)="resetFilters()">
                      <i class="bi bi-arrow-clockwise me-1"></i> Reset Filters
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>

<!-- ********** NEW: Edit Offcanvas ********** -->
<app-offcanvas [title]="'Edit QR Type'" *ngIf="isEditModalOpen" (onClickCross)="closeEditModal()">
  <div class="edit-container p-3">
      <form *ngIf="selectedQrType" #editForm="ngForm" (ngSubmit)="submitEditForm()">
          <div class="row g-3">

              <!-- QR Type ID (Readonly) -->
              <div class="col-12">
                  <label class="form-label" for="editQrTypeId">QR Type ID</label>
                  <input type="text" id="editQrTypeId" class="form-control"
                         [value]="selectedQrType.id" name="id" readonly disabled>
              </div>

              <!-- QR Type Name (Editable) -->
              <div class="col-12">
                  <label class="form-label" for="editQrTypeName">QR Type Name</label>
                  <input type="text" id="editQrTypeName" class="form-control" placeholder="Enter QR Type Name"
                         [(ngModel)]="selectedQrType.title" name="title" required #titleInput="ngModel"
                         pattern="^[a-zA-Z\s]*$" maxlength="30"
                         [ngClass]="{'is-invalid': titleInput.invalid && (titleInput.dirty || titleInput.touched)}">
                   <!-- Validation Messages -->
                   <div *ngIf="titleInput.invalid && (titleInput.dirty || titleInput.touched)" class="invalid-feedback">
                      <div *ngIf="titleInput.errors?.['required']">QR type name is required.</div>
                      <div *ngIf="titleInput.errors?.['pattern']">QR type name should contain only alphabets.</div>
                      <div *ngIf="titleInput.errors?.['maxlength']">QR type name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display -->
                  <small *ngIf="selectedQrType?.title" class="text-muted">
                      {{ selectedQrType.title.length }}/30 characters
                  </small>
              </div>

               <!-- Enabled Status -->
               <div class="col-12">
                   <label class="form-label d-block mb-2">Status</label>
                    <app-switch
                          [(checked)]="selectedQrType.enabled"
                          name="enabled"
                          onLabel="Active"
                          offLabel="Inactive">
                    </app-switch>
               </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeEditModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="editForm.invalid || editLoading">
                       <span *ngIf="!editLoading"><i class="bi bi-save me-1"></i> Save Changes</span>
                       <span *ngIf="editLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                       <span *ngIf="editLoading"> Saving...</span>
                  </button>
              </div>
          </div>
      </form>
      <div *ngIf="!selectedQrType && isEditModalOpen" class="text-center p-5">
           <div class="spinner-border spinner-border-sm" role="status">
               <span class="visually-hidden">Loading form...</span>
           </div>
      </div>
  </div>
</app-offcanvas>
<!-- ********** END: Edit Offcanvas ********** -->

 <!-- ********** NEW: Create Offcanvas ********** -->
<app-offcanvas [title]="'Create New QR Type'" *ngIf="isCreateModalOpen" (onClickCross)="closeCreateModal()">
  <div class="create-container p-3">
      <form #createForm="ngForm" (ngSubmit)="submitCreateForm()">
          <div class="row g-3">

              <!-- QR Type Name (Required) -->
              <div class="col-12">
                  <label class="form-label" for="createQrTypeName">QR Type Name</label>
                  <input type="text" id="createQrTypeName" class="form-control" placeholder="Enter New QR Type Name"
                         [(ngModel)]="newQrTypeData.title" name="title" required #createTitleInput="ngModel"
                         pattern="^[a-zA-Z\s]*$" maxlength="30"
                         [ngClass]="{'is-invalid': createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)}">
                  <!-- Validation Messages -->
                  <div *ngIf="createTitleInput.invalid && (createTitleInput.dirty || createTitleInput.touched)" class="invalid-feedback">
                      <div *ngIf="createTitleInput.errors?.['required']">QR type name is required.</div>
                      <div *ngIf="createTitleInput.errors?.['pattern']">QR type name should contain only alphabets.</div>
                      <div *ngIf="createTitleInput.errors?.['maxlength']">QR type name cannot exceed 30 characters.</div>
                  </div>
                  <!-- Character count display -->
                  <small *ngIf="newQrTypeData.title" class="text-muted">
                      {{ newQrTypeData.title.length }}/30 characters
                  </small>
              </div>

              <!-- Enabled Status (Default to Active/true) -->
              <div class="col-12">
                  <label class="form-label d-block mb-2">Status</label>
                  <app-switch
                        [(checked)]="newQrTypeData.enabled"
                        name="enabled"
                        onLabel="Active"
                        offLabel="Inactive">
                  </app-switch>
              </div>

              <!-- Action Buttons -->
              <div class="col-12 mt-4 d-flex justify-content-end gap-2">
                  <button type="button" class="btn btn-secondary" (click)="closeCreateModal()">
                      Cancel
                  </button>
                  <button type="submit" class="btn adani-btn" [disabled]="createForm.invalid || createLoading">
                      <span *ngIf="!createLoading"><i class="bi bi-plus-circle-fill me-1"></i> Create QR Type</span>
                      <span *ngIf="createLoading" class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                      <span *ngIf="createLoading"> Creating...</span>
                  </button>
              </div>
          </div>
      </form>
  </div>
</app-offcanvas>
<!-- ********** END: Create Offcanvas ********** -->
