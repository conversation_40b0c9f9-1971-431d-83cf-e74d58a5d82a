// Import unified table styles - all styling is now handled globally
@import '../../../../assets/scss/unified-table-styles.scss';

// QR Code Management pattern - no card wrappers
.table-responsive {
    margin-bottom: 24px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #E2E8F0;
}

// Tab header styling to match QR code management pattern
.tab-header-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 16px 0;
    border-bottom: 1px solid #E2E8F0;

    .tab-spacer {
        flex: 1;
    }

    .tab-center {
        flex: 2;
        text-align: center;

        h6 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #2D336B;
        }
    }

    .tab-actions {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 12px;

        .filter-button {
            cursor: pointer;
            transition: transform 0.2s ease;

            &:hover {
                transform: scale(1.1);
            }
        }
    }
}

// Component-specific overrides (if needed)
.table-container {
    // All styling is now handled by unified-table-styles.scss
    // This ensures consistency across all components
}

// Keep component-specific styles
.incident-image {
    width: 100px;
    height: 100px;
    object-fit: cover;
}