@import '../../../assets/scss/variables.scss';

.tab-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: auto;
    margin-bottom: 0;
}

.tab-headers {
    display: flex;
    justify-content: center;
    background-color: $tab-bg;
    border: 1px solid $tab-border;
    border-radius: $tab-border-radius;
    padding: $spacing-xs;
    box-shadow: $shadow-md;
    overflow: hidden;
}

.tab-header {
    padding: $spacing-lg $spacing-3xl;
    cursor: pointer;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $tab-text;
    border-radius: calc(#{$tab-border-radius} - #{$spacing-xs});
    transition: all 0.3s ease;
    white-space: nowrap;
    position: relative;
    overflow: hidden;

    &:hover:not(.active) {
        background-color: $gray-50;
        color: $primary-blue;
        transform: translateY(-1px);
    }

    &.active {
        background: $tab-active-bg;
        color: $tab-text-active;
        font-weight: $font-weight-semibold;
        box-shadow: $shadow-sm;
        transform: translateY(-2px);

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
            pointer-events: none;
        }
    }
}