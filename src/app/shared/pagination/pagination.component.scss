@import '../../../assets/scss/variables.scss';

.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: $spacing-3xl;
    padding: $spacing-lg;
    background-color: $white;
    border-radius: $border-radius-xl;
    box-shadow: $shadow-sm;
}

.modern-pagination {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: $pagination-bg;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-sm;
    overflow: hidden;
    border: 1px solid $pagination-border;
}

.page-item {
    .page-link {
        background-color: transparent;
        border: none;
        color: $pagination-text;
        padding: $spacing-md $spacing-lg;
        text-decoration: none;
        cursor: pointer;
        font-size: $font-size-sm;
        font-weight: $font-weight-medium;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 44px;
        height: 44px;
        position: relative;

        &:hover {
            background-color: $pagination-hover-bg;
            color: $primary-blue;
            transform: translateY(-1px);
        }
    }

    &.active .page-link {
        background: $pagination-active-bg;
        color: $pagination-active-text;
        font-weight: $font-weight-semibold;
        box-shadow: $shadow-sm;
        transform: translateY(-2px);

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
            pointer-events: none;
        }
    }

    &.disabled .page-link {
        opacity: 0.4;
        cursor: not-allowed;

        &:hover {
            background-color: transparent;
            color: $pagination-text;
            transform: none;
        }
    }
}

.pagination-info {
    font-size: $font-size-sm;
    color: $gray-500;
    font-weight: $font-weight-medium;

    .text-primary {
        color: $primary-blue;
        font-weight: $font-weight-semibold;
    }
}

  .page-item.disabled .page-link {
    background-color: #eee;
    color: #aaa;
    cursor: not-allowed;
    border-color: #ddd;
  }

  .bi {
    font-size: 1rem;
  }
  .pagination-info{
    font-size: 12px;
  }