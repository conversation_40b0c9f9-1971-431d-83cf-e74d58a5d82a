@import '../../../assets/scss/variables.scss';

.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    animation: fadeIn 0.3s forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

.custom-modal-content {
    background: $offcanvas-bg;
    border-radius: 20px 20px 20px 20px;
    box-shadow: -4px 0 15px rgba(0, 0, 0, 0.1);
    width: 400px;
    max-width: 400px;
    z-index: 1001;
    position: fixed;
    right: 0;
    top: 0;
    height: 100%;
    transform: translateX(100%);
    animation: slideIn 0.3s ease-out forwards;
    overflow: hidden;
    border: none;
    border-left: 1px solid #E2E8F0;
}

@keyframes slideIn {
    to {
        transform: translateX(0);
    }
}

.custom-modal-header {
    background: $white;
    color: #1E293B;
    padding: 20px 24px 20px 44px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #F1F5F9;
    position: relative;
    min-height: 64px;
    border-radius: 20px 20px 0 0;

    h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #1E293B;
        letter-spacing: 0;
    }
}

.close-button {
    background: $primary-blue;
    border: none;
    color: $white;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    position: absolute;
    left: -20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
    box-shadow: 0 2px 8px rgba(45, 51, 107, 0.3);
    border: 3px solid $white;

    &:hover {
        background-color: #1E2A5E;
        transform: translateY(-50%) scale(1.05);
        box-shadow: 0 4px 12px rgba(45, 51, 107, 0.4);
        border-color: $white;
    }

    &:active {
        transform: translateY(-50%) scale(0.95);
    }
}

.custom-modal-body {
    padding: 24px 24px 24px 44px;
    height: calc(100% - 64px);
    overflow-y: auto;
    background: $white;
    border-radius: 0 0 20px 20px;

    /* Custom scrollbar */
    &::-webkit-scrollbar {
        width: 4px;
    }

    &::-webkit-scrollbar-track {
        background: #F8FAFC;
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: #CBD5E1;
        border-radius: 2px;

        &:hover {
            background: #94A3B8;
        }
    }
}

// ===== FILTER FORM STYLING =====
.filter-container {
    padding: 0;

    .form-label {
        font-size: 13px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
        display: block;
        line-height: 1.3;
    }

    .form-control,
    .form-select {
        background-color: $white;
        border: 1px solid #D1D5DB;
        border-radius: 6px;
        padding: 12px 14px;
        font-size: 14px;
        color: #374151;
        transition: all 0.2s ease;
        width: 100%;
        line-height: 1.4;
        min-height: 44px;

        &:focus {
            outline: none;
            border-color: $primary-blue;
            box-shadow: 0 0 0 3px rgba(45, 51, 107, 0.1);
        }

        &::placeholder {
            color: #9CA3AF;
            font-style: normal;
        }

        &:disabled {
            background-color: #F9FAFB;
            color: #9CA3AF;
            cursor: not-allowed;
        }
    }

    .col-12 {
        margin-bottom: 24px;
    }

    .btn {
        width: 100%;
        padding: 12px 20px;
        font-size: 14px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        line-height: 1.4;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.adani-btn {
            background: $btn-primary-bg;
            color: $btn-primary-text;
            border: none;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                color: $btn-primary-text;
            }

            &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(45, 51, 107, 0.1);
            }

            i {
                margin-right: 8px;
                font-size: 14px;
            }
        }

        &.btn-secondary {
            background-color: $white;
            color: #374151;
            border: 1px solid #D1D5DB;

            &:hover {
                background-color: #F9FAFB;
                border-color: $primary-blue;
                color: $primary-blue;
                transform: translateY(-1px);
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            }

            &:focus {
                outline: none;
                box-shadow: 0 0 0 3px rgba(45, 51, 107, 0.1);
            }

            i {
                margin-right: 8px;
                font-size: 14px;
            }
        }
    }

    .small,
    small {
        font-size: 12px;
        color: #6B7280;
        margin-top: 4px;
        line-height: 1.3;
    }
}