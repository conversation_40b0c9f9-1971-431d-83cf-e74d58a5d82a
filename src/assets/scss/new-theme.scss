/* BOG Angular Application - New Theme Implementation */
@import './variables.scss';

// ===== GLOBAL STYLES =====
body {
  font-family: $font-family-primary;
  background-color: $gray-50;
  color: $gray-700;
  font-size: $font-size-base;
  line-height: $line-height-normal;
}

// ===== SIDEBAR STYLES =====
.sidebar-container {
  background-color: $sidebar-bg;
  border-right: 1px solid $sidebar-border;
  box-shadow: $shadow-sm;
}

#sidebar {
  background-color: $sidebar-bg;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-md;
  
  .list-group-item {
    background-color: transparent;
    border: none;
    color: $sidebar-text;
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    border-radius: $border-radius-md;
    margin: $spacing-xs 0;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: $sidebar-item-hover-bg;
      color: $sidebar-text-active;
    }
    
    &.active {
      background-color: $sidebar-item-active-bg;
      color: $sidebar-text-active;
      font-weight: $font-weight-semibold;
      border-left: 3px solid $sidebar-item-active-border;
    }
  }
  
  .iconStyle {
    color: inherit;
    font-size: $font-size-lg;
    min-width: 20px;
  }
}

// ===== TABLE STYLES =====
.table {
  background-color: $white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #E2E8F0;
  margin-bottom: 0;

  thead th {
    background-color: $table-header-bg;
    color: $table-header-text;
    font-size: 12px;
    font-weight: 600;
    padding: 16px 12px;
    border: none;
    text-align: center;
    vertical-align: middle;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    white-space: nowrap;
  }

  tbody {
    tr {
      border-bottom: 1px solid #F1F5F9;
      transition: all 0.2s ease;

      &:hover {
        background-color: #F8FAFC;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      &:last-child {
        border-bottom: none;
      }
    }

    td {
      padding: 16px 12px;
      font-size: 12px;
      color: #334155;
      vertical-align: middle;
      border: none;
      line-height: 1.4;
    }
  }
}

.table-header th {
  background-color: $table-header-bg !important;
  color: $table-header-text !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  text-align: center !important;
  padding: 16px 12px !important;
  border: none !important;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

// ===== TABLE BORDERED STYLES =====
.table-bordered {
  border: 1px solid #E2E8F0;

  thead th,
  tbody td {
    border: 1px solid #F1F5F9;
  }

  thead th {
    border-bottom: 2px solid #E2E8F0;
  }
}

// ===== TABLE HOVER STYLES =====
.table-hover tbody tr:hover {
  background-color: #F8FAFC;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

// ===== BUTTON STYLES =====
.adani-btn {
  background: $btn-primary-bg;
  color: $btn-primary-text;
  border: none;
  padding: 10px 16px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  line-height: 1.2;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    color: $btn-primary-text;
    text-decoration: none;
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(45, 51, 107, 0.1);
  }

  i {
    margin-right: 4px;
    font-size: 12px;
  }
}

.btn-secondary {
  background-color: $btn-secondary-bg;
  color: $btn-secondary-text;
  border: 1px solid $btn-secondary-border;
  padding: 10px 16px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  line-height: 1.2;
  white-space: nowrap;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 32px;

  &:hover {
    background-color: #F8FAFC;
    border-color: $primary-blue;
    color: $primary-blue;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(45, 51, 107, 0.1);
  }
}

// ===== TAB STYLES =====
.tab-container {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
  width: 100%;
}

.tab-headers {
  display: flex;
  background-color: $tab-bg;
  border: 1px solid #E2E8F0;
  border-radius: 25px;
  padding: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-header {
  padding: 10px 20px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  color: #64748B;
  border-radius: 21px;
  transition: all 0.2s ease;
  white-space: nowrap;
  line-height: 1.2;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(.active) {
    background-color: #F8FAFC;
    color: $primary-blue;
  }

  &.active {
    background: $tab-active-bg;
    color: $tab-text-active;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

// ===== PAGINATION STYLES =====
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 20px;
  padding: 16px 20px;
  background-color: $white;
  border-radius: 8px;
  border: 1px solid #F1F5F9;
}

.modern-pagination {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
  background-color: $pagination-bg;
  border-radius: 6px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #E2E8F0;
}

.page-item {
  .page-link {
    background-color: transparent;
    border: none;
    color: #64748B;
    padding: 8px 12px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    line-height: 1.2;
    text-decoration: none;

    &:hover {
      background-color: #F8FAFC;
      color: $primary-blue;
      transform: translateY(-1px);
    }

    &:focus {
      outline: none;
      box-shadow: 0 0 0 2px rgba(45, 51, 107, 0.1);
    }
  }

  &.active .page-link {
    background: $pagination-active-bg;
    color: $pagination-active-text;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  &.disabled .page-link {
    opacity: 0.4;
    cursor: not-allowed;

    &:hover {
      background-color: transparent;
      color: #64748B;
      transform: none;
    }
  }
}

.pagination-info {
  font-size: 12px;
  color: #64748B;
  font-weight: 500;
  line-height: 1.2;
}

// ===== CARD STYLES =====
.card {
  background-color: $white;
  border: 1px solid #E2E8F0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 24px;

  .card-header {
    background-color: $white;
    border-bottom: 1px solid #F1F5F9;
    padding: 20px 24px;

    h6 {
      font-size: 16px;
      font-weight: 600;
      color: #1E293B;
      margin: 0;
      line-height: 1.25;
    }

    .row {
      margin: 0;

      .col {
        padding: 0;
      }

      .col.text-end {
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 12px;
      }
    }
  }

  .card-body {
    padding: 0;
    background-color: $white;
  }

  .card-footer {
    background-color: #F8FAFC;
    border-top: 1px solid #F1F5F9;
    padding: 16px 24px;
    text-align: center;
  }
}

// ===== OFFCANVAS STYLES =====
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.3s forwards;
}

.custom-modal-content {
  background: $offcanvas-bg;
  border-radius: $border-radius-xl 0 0 $border-radius-xl;
  box-shadow: $offcanvas-shadow;
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  transform: translateX(100%);
  animation: slideIn 0.3s forwards;
  overflow: hidden;

  .custom-modal-header {
    background: $offcanvas-header-bg;
    color: $offcanvas-header-text;
    padding: $spacing-2xl;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h5 {
      font-size: $font-size-xl;
      font-weight: $font-weight-semibold;
      margin: 0;
    }

    .close-button {
      background: none;
      border: none;
      color: $offcanvas-header-text;
      font-size: $font-size-2xl;
      cursor: pointer;
      padding: 0;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: $border-radius-full;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .custom-modal-body {
    padding: $spacing-2xl;
    height: calc(100% - 80px);
    overflow-y: auto;
  }
}

// ===== FORM STYLES =====
.form-label {
  font-size: $font-size-sm;
  font-weight: $font-weight-medium;
  color: $form-label;
  margin-bottom: $spacing-sm;
  display: block;
}

.form-control,
.form-select {
  background-color: $form-bg;
  border: 1px solid $form-border;
  border-radius: $form-border-radius;
  padding: $spacing-md $spacing-lg;
  font-size: $font-size-sm;
  color: $form-text;
  transition: all 0.2s ease;
  width: 100%;

  &:focus {
    outline: none;
    border-color: $form-border-focus;
    box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
  }

  &::placeholder {
    color: $form-placeholder;
  }

  &:disabled {
    background-color: $gray-100;
    color: $gray-500;
    cursor: not-allowed;
  }
}

// ===== BADGE STYLES =====
.badge {
  font-size: $font-size-xs;
  font-weight: $font-weight-medium;
  padding: $spacing-xs $spacing-md;
  border-radius: $border-radius-full;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &.bg-success {
    background-color: $success !important;
    color: $white;
  }

  &.bg-warning {
    background-color: $warning !important;
    color: $white;
  }

  &.bg-danger {
    background-color: $error !important;
    color: $white;
  }

  &.bg-info {
    background-color: $info !important;
    color: $white;
  }
}

// ===== FILTER STYLES =====
.filter-container {
  .row {
    margin-bottom: $spacing-lg;
  }

  .col-12 {
    margin-bottom: $spacing-lg;
  }

  .btn {
    width: 100%;
    margin-bottom: $spacing-md;
  }
}

// ===== DETAILS CONTAINER STYLES =====
.details-container {
  .label-value {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: $spacing-sm 0;
    border-bottom: 1px dotted $gray-300;
    margin-bottom: $spacing-sm;
    font-size: $font-size-sm;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    strong {
      color: $gray-600;
      font-weight: $font-weight-medium;
      flex-shrink: 0;
      margin-right: $spacing-md;
    }

    .value-text {
      color: $gray-800;
      font-weight: $font-weight-semibold;
      text-align: right;
      word-break: break-word;
    }
  }
}

// ===== ANIMATIONS =====
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  to {
    transform: translateX(0);
  }
}

// ===== ENHANCED FORM CONTROLS =====
.form-group {
  margin-bottom: $spacing-2xl;

  .form-label {
    font-size: $font-size-sm;
    font-weight: $font-weight-semibold;
    color: $gray-700;
    margin-bottom: $spacing-sm;
    display: block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .form-control,
  .form-select {
    background-color: $form-bg;
    border: 2px solid $gray-200;
    border-radius: $border-radius-lg;
    padding: $spacing-lg;
    font-size: $font-size-sm;
    color: $form-text;
    transition: all 0.2s ease;
    width: 100%;
    font-weight: $font-weight-medium;

    &:focus {
      outline: none;
      border-color: $form-border-focus;
      box-shadow: 0 0 0 4px rgba(74, 108, 247, 0.1);
      transform: translateY(-1px);
    }

    &::placeholder {
      color: $form-placeholder;
      font-style: italic;
      font-weight: $font-weight-normal;
    }

    &:disabled {
      background-color: $gray-100;
      color: $gray-500;
      cursor: not-allowed;
      border-color: $gray-200;
    }
  }

  .form-text {
    font-size: $font-size-xs;
    color: $gray-500;
    margin-top: $spacing-xs;
  }

  &.has-error {
    .form-control,
    .form-select {
      border-color: $error;
      box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
    }

    .form-text {
      color: $error;
    }
  }

  &.has-success {
    .form-control,
    .form-select {
      border-color: $success;
      box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
    }
  }
}

// ===== CHECKBOX AND RADIO STYLING =====
.form-check {
  margin-bottom: $spacing-lg;

  .form-check-input {
    width: 20px;
    height: 20px;
    border: 2px solid $gray-300;
    border-radius: $border-radius-sm;
    background-color: $white;
    transition: all 0.2s ease;

    &:checked {
      background-color: $primary-blue;
      border-color: $primary-blue;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20' fill='white'%3e%3cpath fill-rule='evenodd' d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z' clip-rule='evenodd'/%3e%3c/svg%3e");
    }

    &:focus {
      box-shadow: 0 0 0 4px rgba(74, 108, 247, 0.1);
    }
  }

  .form-check-label {
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    color: $gray-700;
    margin-left: $spacing-sm;
  }
}

// ===== DROPDOWN STYLING =====
.dropdown {
  .dropdown-toggle {
    background: $btn-primary-bg;
    color: $btn-primary-text;
    border: none;
    padding: $spacing-md $spacing-xl;
    font-size: $font-size-sm;
    font-weight: $font-weight-medium;
    border-radius: $btn-border-radius;
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: $shadow-md;
    }

    &::after {
      margin-left: $spacing-sm;
    }
  }

  .dropdown-menu {
    background-color: $white;
    border: 1px solid $gray-200;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-lg;
    padding: $spacing-sm 0;
    margin-top: $spacing-xs;

    .dropdown-item {
      padding: $spacing-md $spacing-xl;
      font-size: $font-size-sm;
      color: $gray-700;
      transition: all 0.2s ease;

      &:hover {
        background-color: $gray-50;
        color: $primary-blue;
      }

      &:active {
        background-color: $primary-blue;
        color: $white;
      }
    }
  }
}

// ===== RESPONSIVE UTILITIES =====
@media (max-width: 768px) {
  .tab-header {
    padding: $spacing-sm $spacing-lg;
    font-size: $font-size-xs;
  }

  .custom-modal-content {
    width: 100% !important;
    border-radius: 0;
  }

  .sidebar-container {
    #sidebar {
      width: 100%;
    }
  }

  .form-group {
    .form-control,
    .form-select {
      padding: $spacing-md;
      font-size: $font-size-sm;
    }
  }
}
