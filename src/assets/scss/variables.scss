/* BOG Angular Application - New Theme Variables */

// ===== PRIMARY BRAND COLORS =====
$primary-blue: #2D336B;
$primary-secondary: #41559B;
$primary-gradient: linear-gradient(133.33deg, #2D336B 16.35%, #41559B 84.17%);

// ===== NEUTRAL COLORS =====
$white: #FFFFFF;
$gray-50: #F8FAFC;
$gray-100: #F1F5F9;
$gray-200: #E2E8F0;
$gray-300: #CBD5E1;
$gray-400: #94A3B8;
$gray-500: #64748B;
$gray-600: #475569;
$gray-700: #334155;
$gray-800: #1E293B;
$gray-900: #0F172A;

// ===== SEMANTIC COLORS =====
$success: #10B981;
$warning: #F59E0B;
$error: #EF4444;
$info: #3B82F6;

// ===== SIDEBAR THEME =====
$sidebar-width: 250px;
$sidebar-collapsed-width: 60px;
$sidebar-bg: $white;
$sidebar-border: $gray-200;
$sidebar-text: $gray-700;
$sidebar-text-active: $primary-blue;
$sidebar-item-hover-bg: $gray-50;
$sidebar-item-active-bg: $gray-100;
$sidebar-item-active-border: $primary-blue;

// ===== TABLE THEME =====
$table-header-bg: $primary-blue;
$table-header-text: $white;
$table-border: #E2E8F0;
$table-row-hover: #F8FAFC;
$table-text: #334155;
$table-text-secondary: #64748B;

// ===== BUTTON THEME =====
$btn-primary-bg: $primary-gradient;
$btn-primary-text: $white;
$btn-secondary-bg: $white;
$btn-secondary-text: #334155;
$btn-secondary-border: #E2E8F0;
$btn-border-radius: 6px;

// ===== TAB THEME =====
$tab-bg: $white;
$tab-border: #E2E8F0;
$tab-text: #64748B;
$tab-text-active: $white;
$tab-active-bg: $primary-gradient;
$tab-border-radius: 25px;

// ===== PAGINATION THEME =====
$pagination-bg: $white;
$pagination-border: #E2E8F0;
$pagination-text: #64748B;
$pagination-active-bg: $primary-gradient;
$pagination-active-text: $white;
$pagination-hover-bg: #F8FAFC;

// ===== OFFCANVAS/MODAL THEME =====
$offcanvas-bg: $white;
$offcanvas-header-bg: $primary-gradient;
$offcanvas-header-text: $white;
$offcanvas-border: $gray-200;
$offcanvas-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);

// ===== FORM THEME =====
$form-bg: $white;
$form-border: #E2E8F0;
$form-border-focus: $primary-blue;
$form-text: #334155;
$form-placeholder: #94A3B8;
$form-label: #475569;
$form-border-radius: 6px;

// ===== TYPOGRAPHY =====
$font-family-primary: 'adani', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
$font-size-xs: 10px;
$font-size-sm: 12px;
$font-size-base: 14px;
$font-size-lg: 16px;
$font-size-xl: 18px;
$font-size-2xl: 20px;
$font-size-3xl: 24px;

$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// ===== SPACING =====
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 20px;
$spacing-2xl: 24px;
$spacing-3xl: 32px;
$spacing-4xl: 40px;

// ===== SHADOWS =====
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

// ===== BORDER RADIUS =====
$border-radius-sm: 4px;
$border-radius-md: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$border-radius-2xl: 16px;
$border-radius-full: 50%;

// ===== LEGACY COMPATIBILITY (for gradual migration) =====
$theme1-primary: $primary-blue;
$theme1-main-heading: $gray-800;
$theme1-sub-heading: $gray-700;
$theme1-main-background: $gray-50;
$theme1-sub-background: $white;
$theme1-table-data: $gray-700;
$theme1-shadow: $gray-200;

// Sidebar legacy
$sideBarWidth: $sidebar-width;
$subMenuBg: $gray-100;
$subMenuHover: $sidebar-item-hover-bg;
$subMenuActiveText: $sidebar-text-active;
$menuBg: $sidebar-bg;
$menuText: $sidebar-text;
$menuActiveText: $sidebar-text-active;

// Login page legacy
$lightGray: $gray-300;
$darkGray: $gray-600;
$loginBg: $gray-500;
$loginCursorColor: $gray-300;
$textAreaBottom: $gray-300;

// The :export directive is the magic sauce for webpack
// https://mattferderer.com/use-sass-variables-in-typescript-and-javascript
:export {
  menuBg: $menuBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
}
