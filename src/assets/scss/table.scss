@import './variables.scss';

.mat-mdc-header-row {
    height: 48px !important;
    background: $table-header-bg !important;
    color: $table-header-text !important;
}

.mat-mdc-row {
    height: 48px !important;
    transition: background-color 0.2s ease;

    &:hover {
        background-color: $table-row-hover !important;
    }
}

thead th {
    font-family: $font-family-primary !important;
    font-size: 12px !important;
    color: $table-header-text !important;
    font-weight: 600 !important;
    padding: 16px 12px !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center !important;
    vertical-align: middle !important;
    border: none !important;
    line-height: 1.2;
    white-space: nowrap;
}

tbody td {
    font-size: 12px !important;
    font-weight: 500 !important;
    font-family: $font-family-primary;
    color: #334155 !important;
    padding: 16px 12px !important;
    vertical-align: middle !important;
    border: none !important;
    line-height: 1.4;
}

.table-title {
    font-weight: $font-weight-semibold !important;
    font-family: $font-family-primary;
    color: $gray-800;
    font-size: $font-size-lg;
}

/* common */
.export, .column-selection, .filter {
    height: 35px;
    width: 35px;
    border: 1px solid #E2E8F0;
    margin-left: 12px;
    background: $white;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    img {
        width: 18px;
        height: 18px;
        padding: 0;
    }
}

.export:hover, .column-selection:hover, .filter:hover {
    background: #F8FAFC;
    border-color: $primary-blue;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Search */
::ng-deep.mat-mdc-form-field-flex, .mat-mdc-form-field-flex {
    height: 40px !important;
}

::ng-deep.mat-mdc-form-field-infix {
    margin-top: -14px !important;
}

::ng-deep.mat-mdc-form-field-subscript-wrapper {
    height: 0px !important;
}

::ng-deep.mat-mdc-text-field-wrapper {
    background: $form-bg;
    border: 1px solid $form-border;
    border-radius: $form-border-radius;
    transition: border-color 0.2s ease;

    &:focus-within {
        border-color: $form-border-focus;
        box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.1);
    }
}