/* BOG Angular Application - Unified Table Styles */
/* This file provides consistent table styling across all components */
@import './variables.scss';

// ===== UNIFIED TABLE STYLES =====
// These styles match the design image provided and ensure consistency

.unified-table,
.table,
.custom-table {
  width: 100%;
  border-collapse: collapse;
  background-color: $white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #E2E8F0;
  margin-bottom: 0;
  font-family: $font-family-primary;

  // ===== TABLE HEADER STYLES =====
  thead th,
  .table-header th {
    background-color: $table-header-bg !important;
    color: $table-header-text !important;
    font-size: 12px !important;
    font-weight: 600 !important;
    padding: 12px 8px !important;
    text-align: center !important;
    vertical-align: middle !important;
    border: 1px solid #E2E8F0 !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.2;
    white-space: nowrap;
    font-family: $font-family-primary !important;
  }

  // ===== TABLE BODY STYLES =====
  tbody {
    tr {
      background-color: $white;
      border-bottom: 1px solid #E2E8F0;
      transition: all 0.2s ease;

      &:hover {
        background-color: #F8FAFC !important;
      }

      &:last-child {
        border-bottom: 1px solid #E2E8F0;
      }
    }

    td {
      font-size: 12px !important;
      font-weight: 500 !important;
      font-family: $font-family-primary !important;
      color: #334155 !important;
      padding: 12px 8px !important;
      vertical-align: middle !important;
      border: 1px solid #E2E8F0 !important;
      line-height: 1.4;
      text-align: center !important;
    }
  }
}

// ===== TABLE CONTAINER STYLES =====
.table-container {
  width: 100%;
  overflow-x: auto;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  table {
    min-width: 800px; // Ensure minimum width for proper display
  }
}

.table-responsive {
  overflow-x: auto;
  white-space: nowrap;
  max-width: 100%;
  border-radius: 12px;
}

// ===== ACTION COLUMN STYLES =====
.actions {
  text-align: center !important;
  vertical-align: middle !important;
  width: 120px; // Fixed width for action column
  
  .btn {
    margin: 0 2px;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    min-width: 32px;
    height: 28px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    &.btn-sm {
      padding: 2px 6px;
      font-size: 11px;
      min-width: 28px;
      height: 24px;
    }
  }
  
  i {
    font-size: 12px;
  }
  
  // Edit button styling
  .edit-btn,
  .btn-warning {
    background-color: #F59E0B;
    border-color: #F59E0B;
    color: white;
    
    &:hover {
      background-color: #D97706;
      border-color: #D97706;
    }
  }
  
  // Delete button styling
  .delete-btn,
  .btn-danger {
    background-color: #EF4444;
    border-color: #EF4444;
    color: white;
    
    &:hover {
      background-color: #DC2626;
      border-color: #DC2626;
    }
  }
}

// ===== QR CODE COLUMN STYLES =====
.qr-code-cell {
  text-align: center !important;
  vertical-align: middle !important;
  width: 80px; // Fixed width for QR code column
  
  img {
    width: 40px;
    height: 40px;
    border: 1px solid #E2E8F0;
    border-radius: 4px;
    object-fit: contain;
  }
}

// ===== STATUS COLUMN STYLES =====
.status-cell {
  text-align: center !important;
  vertical-align: middle !important;
  
  .badge {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
}

// ===== PLANT CHIP STYLES IN TABLES =====
.plant-chip {
  background-color: $primary-blue;
  color: white !important;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  display: inline-block;
  margin: 2px;
}

// ===== ROLE BADGE STYLES IN TABLES =====
.role-badge {
  background-color: $primary-secondary;
  color: white !important;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  display: inline-block;
}

// ===== DETAILS CELL STYLES (for plant-management style) =====
.details-cell {
  text-align: left !important;
  max-width: 300px;
  vertical-align: middle !important;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  padding: 12px !important;
  
  .details-container {
    display: block;
    width: 100%;
    text-align: left;
  }
  
  .label-value {
    margin-bottom: 8px;
    line-height: 1.5;
    display: flex;
    align-items: baseline;
    padding: 2px 0;
    border-bottom: 1px dashed #eee;
    padding-bottom: 8px;
    justify-content: flex-start;
    
    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }
    
    strong {
      font-weight: 500;
      color: #777;
      margin-right: 8px;
      min-width: 100px;
      width: 100px;
      font-size: 12px;
      display: inline-block;
      vertical-align: top;
    }
    
    .value-text {
      font-weight: 600;
      color: #222;
      font-size: 12px;
      display: inline-block;
      word-break: break-word;
      overflow-wrap: break-word;
      flex: 1;
      letter-spacing: normal;
      line-height: 1.5;
      text-align: left;
    }
  }
}

// ===== RESPONSIVE TABLE STYLES =====
@media (max-width: 768px) {
  .table-container {
    .unified-table,
    .table,
    .custom-table {
      font-size: 11px;
      
      thead th,
      .table-header th {
        padding: 8px 4px !important;
        font-size: 11px !important;
      }
      
      tbody td {
        padding: 8px 4px !important;
        font-size: 11px !important;
      }
    }
  }
  
  .actions {
    width: 100px;
    
    .btn {
      padding: 2px 4px;
      font-size: 10px;
      min-width: 24px;
      height: 24px;
    }
  }
  
  .qr-code-cell {
    width: 60px;
    
    img {
      width: 30px;
      height: 30px;
    }
  }
}

// ===== LOADING STATE STYLES =====
.table-loading {
  text-align: center !important;
  padding: 40px !important;
  color: #64748B;
  font-style: italic;
  
  .spinner-border {
    margin-right: 8px;
  }
}

// ===== EMPTY STATE STYLES =====
.table-empty {
  text-align: center !important;
  padding: 40px !important;
  color: #64748B;
  font-style: italic;
}
