# BOG Angular Project - Complete Table Inventory

## Summary
This document provides a comprehensive inventory of all components in the BOG Angular project that contain tables, categorized by their current styling approach and update priority.

## Legend
- ✅ **Updated**: Component already follows QR code management pattern
- ❌ **Needs Update**: Component uses card wrappers or inconsistent styling
- 🔍 **Needs Investigation**: Component may have tables but requires verification

## Master Management Components

### ✅ Already Updated (12 components)
1. `src/app/components/master-management/plant-type/`
2. `src/app/components/master-management/designation/`
3. `src/app/components/master-management/equipment/`
4. `src/app/components/master-management/opco/`
5. `src/app/components/master-management/root-cause/`
6. `src/app/components/master-management/segment/`
7. `src/app/components/master-management/inspection-tool/`
8. `src/app/components/master-management/area/`
9. ✅ `src/app/components/master-management/cluster/` - **COMPLETED**
10. ✅ `src/app/components/master-management/location-type/` - **COMPLETED**
11. ✅ `src/app/components/master-management/qr-type/` - **COMPLETED**
12. ✅ `src/app/components/master-management/incident-master/` - **COMPLETED**

### ❌ Need Updates (5 components)
1. `src/app/components/master-management/body-part/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `body-part.component.html`, `body-part.component.scss`

2. `src/app/components/master-management/department/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `department.component.html`, `department.component.scss`

3. `src/app/components/master-management/location/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `location.component.html`, `location.component.scss`

4. `src/app/components/master-management/recommended-type/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `recommended-type.component.html`, `recommended-type.component.scss`

5. `src/app/components/master-management/body-part/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `body-part.component.html`, `body-part.component.scss`

6. `src/app/components/master-management/department/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `department.component.html`, `department.component.scss`

7. `src/app/components/master-management/location/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `location.component.html`, `location.component.scss`

8. `src/app/components/master-management/recommended-type/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `recommended-type.component.html`, `recommended-type.component.scss`

9. `src/app/components/master-management/relatesto/`
   - **Current**: Uses old table styling pattern
   - **Priority**: Medium
   - **Files**: `relatesto.component.html`, `relatesto.component.scss`

## Admin Management Components

### ❌ All Need Updates (5 components)
1. `src/app/components/admin-management/active-user/`
   - **Current**: Uses custom-table with card wrapper
   - **Priority**: High
   - **Files**: `active-user.component.html`, `active-user.component.scss`

2. `src/app/components/admin-management/delete-user/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `delete-user.component.html`, `delete-user.component.scss`

3. `src/app/components/admin-management/inactive-user/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `inactive-user.component.html`, `inactive-user.component.scss`

4. `src/app/components/admin-management/roles/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `roles.component.html`, `roles.component.scss`

5. `src/app/components/admin-management/transfer-request/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `transfer-request.component.html`, `transfer-request.component.scss`

## Report Management Components

### ❌ All Need Updates (8 components)
1. `src/app/components/report-management/bog-observation/plantwise-report/`
   - **Current**: Uses custom-table with card wrapper
   - **Priority**: High
   - **Files**: `plantwise-report.component.html`, `plantwise-report.component.scss`

2. `src/app/components/report-management/bog-observation/userwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `userwise-report.component.html`, `userwise-report.component.scss`

3. `src/app/components/report-management/bog-rag/plantwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `plantwise-report.component.html`, `plantwise-report.component.scss`

4. `src/app/components/report-management/bog-rag/userwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `userwise-report.component.html`, `userwise-report.component.scss`

5. `src/app/components/report-management/bog-tour/plantwise-report/`
   - **Current**: Uses custom-table with card wrapper
   - **Priority**: High
   - **Files**: `plantwise-report.component.html`, `plantwise-report.component.scss`

6. `src/app/components/report-management/bog-tour/userwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `userwise-report.component.html`, `userwise-report.component.scss`

7. `src/app/components/report-management/bog-zone/plantwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `plantwise-report.component.html`, `plantwise-report.component.scss`

8. `src/app/components/report-management/bog-zone/userwise-report/`
   - **Current**: Likely uses card wrapper
   - **Priority**: High
   - **Files**: `userwise-report.component.html`, `userwise-report.component.scss`

## Digisafe Components

### ❌ All Need Updates (3 components)
1. `src/app/components/digisafe/gmr-dashboard/`
   - **Current**: Uses custom-table with card wrapper
   - **Priority**: High
   - **Files**: `gmr-dashboard.component.html`, `gmr-dashboard.component.scss`

2. `src/app/components/digisafe/manage-digisafe/`
   - **Current**: Uses card wrapper with table
   - **Priority**: High
   - **Files**: `manage-digisafe.component.html`, `manage-digisafe.component.scss`

3. `src/app/components/digisafe/mis-dashboard/`
   - **Current**: Uses card wrapper with table
   - **Priority**: High
   - **Files**: `mis-dashboard.component.html`, `mis-dashboard.component.scss`

## Other Components

### ❌ Need Updates (7 components)
1. `src/app/components/crisis-management/`
   - **Current**: Uses custom-table with card wrapper
   - **Priority**: High
   - **Files**: `crisis-management.component.html`, `crisis-management.component.scss`

2. `src/app/components/leaderboard/`
   - **Current**: Uses custom-leaderboard-table with card wrapper
   - **Priority**: High
   - **Files**: `leaderboard.component.html`, `leaderboard.component.scss`

3. `src/app/components/notification-management/`
   - **Current**: Uses card wrapper with table
   - **Priority**: High
   - **Files**: `notification-management.component.html`, `notification-management.component.scss`

4. `src/app/components/plant-management/`
   - **Current**: Uses card wrapper with table
   - **Priority**: High
   - **Files**: `plant-management.component.html`, `plant-management.component.scss`

5. `src/app/components/tour-management/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `tour-management.component.html`, `tour-management.component.scss`

6. `src/app/components/incident-management/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `incident-management.component.html`, `incident-management.component.scss`

7. `src/app/components/manage-observation/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `manage-observation.component.html`, `manage-observation.component.scss`

8. `src/app/components/other-task/`
   - **Current**: Likely uses card wrapper
   - **Priority**: Medium
   - **Files**: `other-task.component.html`, `other-task.component.scss`

### ✅ Already Correct (1 component)
1. `src/app/components/qrcode-management/`
   - **Current**: Reference pattern - no card wrapper
   - **Status**: ✅ Perfect example to follow

## Dashboard Components

### 🔍 Special Cases (2 components)
1. `src/app/components/dashboard/cluster-wise-bog-tour/cluster-wise-bog-tour-dialog/`
   - **Current**: Uses Material Design table (mat-table)
   - **Priority**: Low (different pattern, may not need changes)
   - **Files**: `cluster-wise-bog-tour.dialog.component.html`, `cluster-wise-bog-tour.dialog.component.scss`

2. `src/app/components/dashboard/dashboard.component.html`
   - **Current**: No tables, only dashboard cards
   - **Status**: ✅ No changes needed

## Total Count
- **✅ Already Updated**: 13 components (including QR code management reference)
- **❌ Need Updates**: 36 components
- **🔍 Special Cases**: 2 components
- **Total Components with Tables**: 51 components

## Recent Updates Completed
The following 4 master management components have been successfully updated to follow the QR code management table pattern:

1. **cluster/cluster.component** ✅
   - Removed card wrapper structure
   - Applied tab-header-container pattern
   - Updated table to use unified styling
   - Removed ID column from table display
   - Updated SCSS to import unified table styles

2. **location-type/location-type.component** ✅
   - Removed card wrapper structure
   - Applied tab-header-container pattern
   - Updated table to use unified styling
   - Removed ID column from table display
   - Updated SCSS to import unified table styles

3. **qr-type/qr-type.component** ✅
   - Removed card wrapper structure
   - Applied tab-header-container pattern
   - Updated table to use unified styling
   - Removed ID column from table display
   - Updated SCSS to import unified table styles

4. **incident-master/incident-master.component** ✅
   - Removed card wrapper structure
   - Applied tab-header-container pattern
   - Updated table to use unified styling
   - Removed ID column from table display
   - Updated SCSS to import unified table styles
   - Preserved component-specific incident-image styling

## Implementation Priority

### Phase 1 - High Priority (22 components)
Focus on components with confirmed table usage and card wrappers:
- Master Management remaining (4 components)
- Admin Management (5 components)
- Report Management (8 components)
- Digisafe (3 components)
- Other high-priority (2 components: crisis-management, leaderboard)

### Phase 2 - Medium Priority (18 components)
Components that likely have tables but need verification:
- Remaining Master Management (5 components)
- Remaining Other Components (6 components)
- Plant Management (1 component)
- Notification Management (1 component)
- Tour Management (1 component)
- Incident Management (1 component)
- Manage Observation (1 component)
- Other Task (1 component)

### Phase 3 - Special Cases (2 components)
Components with unique requirements or Material Design tables
